<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- TV features -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <application
        android:name=".TVPlayerApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.TVPlayer"
        android:banner="@drawable/app_banner"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".ui.player.PlayerActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer" />

        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer" />

        <activity
            android:name=".ui.details.VideoDetailsActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer" />

        <activity
            android:name=".ui.player.VideoPlayerActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer" />

        <activity
            android:name=".ui.player.ExoPlayerVideoActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.TVPlayer" />

    </application>

</manifest>

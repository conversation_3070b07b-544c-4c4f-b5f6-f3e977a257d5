package com.tvplayer.webdav.ui.player

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.tvplayer.webdav.R
import dagger.hilt.android.AndroidEntryPoint

/**
 * 视频播放器Activity
 * 使用GSYVideoPlayer进行视频播放，如果有问题则可以切换到ExoPlayer
 */
@AndroidEntryPoint
class VideoPlayerActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_VIDEO_PATH = "extra_video_path"
        const val EXTRA_VIDEO_TITLE = "extra_video_title"
    }

    private var videoPlayer: com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_player)

        // 隐藏状态栏和导航栏，全屏播放
        window.decorView.systemUiVisibility = (
            android.view.View.SYSTEM_UI_FLAG_FULLSCREEN
            or android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )

        initVideoPlayer()
        setupVideo()
    }

    private fun initVideoPlayer() {
        try {
            videoPlayer = findViewById(R.id.video_player)
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error initializing GSYVideoPlayer", e)
            // 如果GSYVideoPlayer初始化失败，显示错误并退出
            android.widget.Toast.makeText(this, "视频播放器初始化失败", android.widget.Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun setupVideo() {
        val videoPath = intent.getStringExtra(EXTRA_VIDEO_PATH)
        val videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "视频播放"

        if (videoPath.isNullOrEmpty()) {
            android.util.Log.e("VideoPlayerActivity", "Video path is null or empty")
            finish()
            return
        }

        android.util.Log.d("VideoPlayerActivity", "Setting up video: $videoPath")

        try {
            videoPlayer?.let { player ->
                // 设置视频URL和标题
                player.setUp(videoPath, true, videoTitle)

                // 设置全屏播放
                player.isFullViewContainer = true

                // 自动开始播放
                player.startPlayLogic()
            } ?: run {
                android.util.Log.e("VideoPlayerActivity", "Video player is null")
                android.widget.Toast.makeText(this, "视频播放器未初始化", android.widget.Toast.LENGTH_SHORT).show()
                finish()
            }
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error setting up video", e)
            android.widget.Toast.makeText(this, "视频设置失败: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    override fun onBackPressed() {
        // 处理返回键，如果是全屏状态则退出全屏，否则退出Activity
        try {
            if (com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer.backFromWindowFull(this)) {
                return
            }
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error handling back press", e)
        }
        super.onBackPressed()
    }

    override fun onPause() {
        super.onPause()
        try {
            videoPlayer?.onVideoPause()
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error pausing video", e)
        }
    }

    override fun onResume() {
        super.onResume()
        try {
            videoPlayer?.onVideoResume()
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error resuming video", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer.releaseAllVideos()
        } catch (e: Exception) {
            android.util.Log.e("VideoPlayerActivity", "Error releasing videos", e)
        }
    }
}

package com.tvplayer.webdav.ui.player

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.tvplayer.webdav.R
import dagger.hilt.android.AndroidEntryPoint

/**
 * 视频播放器Activity
 * 使用GSYVideoPlayer进行视频播放
 */
@AndroidEntryPoint
class VideoPlayerActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_VIDEO_PATH = "extra_video_path"
        const val EXTRA_VIDEO_TITLE = "extra_video_title"
    }

    private lateinit var videoPlayer: StandardGSYVideoPlayer

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_player)

        // 隐藏状态栏和导航栏，全屏播放
        window.decorView.systemUiVisibility = (
            android.view.View.SYSTEM_UI_FLAG_FULLSCREEN
            or android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )

        initVideoPlayer()
        setupVideo()
    }

    private fun initVideoPlayer() {
        videoPlayer = findViewById(R.id.video_player)
    }

    private fun setupVideo() {
        val videoPath = intent.getStringExtra(EXTRA_VIDEO_PATH)
        val videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "视频播放"

        if (videoPath.isNullOrEmpty()) {
            android.util.Log.e("VideoPlayerActivity", "Video path is null or empty")
            finish()
            return
        }

        android.util.Log.d("VideoPlayerActivity", "Setting up video: $videoPath")

        // 设置视频URL和标题
        videoPlayer.setUp(videoPath, true, videoTitle)
        
        // 设置全屏播放
        videoPlayer.isFullViewContainer = true
        
        // 自动开始播放
        videoPlayer.startPlayLogic()
    }

    override fun onBackPressed() {
        // 处理返回键，如果是全屏状态则退出全屏，否则退出Activity
        if (StandardGSYVideoPlayer.backFromWindowFull(this)) {
            return
        }
        super.onBackPressed()
    }

    override fun onPause() {
        super.onPause()
        videoPlayer.onVideoPause()
    }

    override fun onResume() {
        super.onResume()
        videoPlayer.onVideoResume()
    }

    override fun onDestroy() {
        super.onDestroy()
        StandardGSYVideoPlayer.releaseAllVideos()
    }
}

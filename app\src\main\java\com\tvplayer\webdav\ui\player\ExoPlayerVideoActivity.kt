package com.tvplayer.webdav.ui.player

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.tvplayer.webdav.R
import dagger.hilt.android.AndroidEntryPoint

/**
 * 备用视频播放器Activity
 * 使用ExoPlayer进行视频播放（如果GSYVideoPlayer有问题时使用）
 */
@AndroidEntryPoint
class ExoPlayerVideoActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_VIDEO_PATH = "extra_video_path"
        const val EXTRA_VIDEO_TITLE = "extra_video_title"
    }

    private var exoPlayer: ExoPlayer? = null
    private lateinit var playerView: StyledPlayerView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_exoplayer_video)

        // 隐藏状态栏和导航栏，全屏播放
        window.decorView.systemUiVisibility = (
            android.view.View.SYSTEM_UI_FLAG_FULLSCREEN
            or android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )

        initializePlayer()
    }

    private fun initializePlayer() {
        playerView = findViewById(R.id.exo_player_view)
        
        val videoPath = intent.getStringExtra(EXTRA_VIDEO_PATH)
        val videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "视频播放"

        if (videoPath.isNullOrEmpty()) {
            android.util.Log.e("ExoPlayerVideoActivity", "Video path is null or empty")
            finish()
            return
        }

        android.util.Log.d("ExoPlayerVideoActivity", "Setting up video: $videoPath")

        exoPlayer = ExoPlayer.Builder(this).build()
        playerView.player = exoPlayer

        val mediaItem = MediaItem.fromUri(videoPath)
        exoPlayer?.setMediaItem(mediaItem)
        exoPlayer?.prepare()
        exoPlayer?.playWhenReady = true
    }

    override fun onPause() {
        super.onPause()
        exoPlayer?.pause()
    }

    override fun onResume() {
        super.onResume()
        exoPlayer?.play()
    }

    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
        exoPlayer = null
    }
}
